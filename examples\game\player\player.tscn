[gd_scene load_steps=3 format=3 uid="uid://c186aybk0cgr4"]

[ext_resource type="Script" uid="uid://bdgtgv5plcpgc" path="res://player/camera.gd" id="1_1ew6o"]

[sub_resource type="CylinderShape3D" id="CylinderShape3D_42akx"]
margin = 0.005
height = 1.75

[node name="Player" type="Player"]
_import_path = NodePath("")
unique_name_in_owner = false
process_mode = 0
process_priority = 0
process_physics_priority = 0
process_thread_group = 0
physics_interpolation_mode = 0
auto_translate_mode = 0
editor_description = ""
script = null

[node name="PlayerShape" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.875, 0)
shape = SubResource("CylinderShape3D_42akx")

[node name="CameraYaw" type="Node3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.65, 0)

[node name="Camera" type="Camera3D" parent="CameraYaw"]
script = ExtResource("1_1ew6o")

[node name="MouseMove" type="Node3D" parent="CameraYaw"]
