package example

import "godot:godot"
import "godot:gdextension"

// see gdextension.InitializationFunction
@(export)
example_library_init :: proc "c" (
    get_proc_address: gdextension.ExtensionInterfaceGetProcAddress,
    library: gdextension.ExtensionClassLibraryPtr,
    initialization: ^gdextension.Initialization,
) -> bool {
    // gdextension procs MUST be initialized before using the binding!
    gdextension.init(library, get_proc_address)

    // Initialize godot context BEFORE calling any godot functions
    context = gdextension.godot_context()

    // Debug: Print that we're initializing (AFTER context is set)
    msg := godot.new_string_cstring("Hello-GDExtension: Library init called!")
    defer godot.free_string(msg)
    godot.gd_print(godot.variant_from(&msg))

    // MUST be called before using any core classes, singletons, or utility functions
    godot.init()

    // Temporarily disable class registration to test basic loading
    initialization.initialize = nil
    initialization.deinitialize = nil
    initialization.user_data = nil
    initialization.minimum_initialization_level = .Core

    return true
}

initialize_example_module :: proc "c" (user_data: rawptr, level: gdextension.InitializationLevel) {
    context = gdextension.godot_context()

    // Debug: Print initialization level
    level_msg := godot.new_string_cstring("Hello-GDExtension: Initialize called with level")
    defer godot.free_string(level_msg)
    godot.gd_print(godot.variant_from(&level_msg))

    if level != .Scene {
        return
    }

    // Debug: Print that we're registering the class
    register_msg := godot.new_string_cstring("Hello-GDExtension: Registering ExampleClass")
    defer godot.free_string(register_msg)
    godot.gd_print(godot.variant_from(&register_msg))

    example_class_register()

    // Debug: Print that registration is complete
    complete_msg := godot.new_string_cstring("Hello-GDExtension: ExampleClass registration complete")
    defer godot.free_string(complete_msg)
    godot.gd_print(godot.variant_from(&complete_msg))
}

uninitialize_example_module :: proc "c" (user_data: rawptr, level: gdextension.InitializationLevel) {
    context = gdextension.godot_context()

    if level != .Scene {
        return
    }
}