package minimal

import "godot:gdextension"
import "godot:godot"

@(export)
example_library_init :: proc "c" (
    get_proc_address: gdextension.ExtensionInterfaceGetProcAddress,
    library: gdextension.ExtensionClassLibraryPtr,
    initialization: ^gdextension.Initialization,
) -> bool {
    // Initialize GDExtension ONLY
    gdextension.init(library, get_proc_address)

    // Set up initialization callbacks
    initialization.initialize = initialize_module
    initialization.deinitialize = deinitialize_module
    initialization.user_data = nil
    initialization.minimum_initialization_level = .Scene

    return true
}

initialize_module :: proc "c" (user_data: rawptr, level: gdextension.InitializationLevel) {
    context = gdextension.godot_context()

    if level != .Scene {
        return
    }

    // Initialize Godot ONLY when we need it (in the initialize callback)
    godot.init()

    // Print success message
    msg := godot.new_string_cstring("SUCCESS: GDExtension loaded and initialized!")
    defer godot.free_string(msg)
    godot.gd_print(godot.variant_from(&msg))
}

deinitialize_module :: proc "c" (user_data: rawptr, level: gdextension.InitializationLevel) {
    context = gdextension.godot_context()
    // Nothing to clean up yet
}
