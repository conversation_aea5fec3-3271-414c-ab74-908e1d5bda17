package game

import "godot:godot"
import "godot:gdextension"

@(export)
game_init :: proc "c" (
    get_proc_address: gdextension.ExtensionInterfaceGetProcAddress,
    library: gdextension.ExtensionClassLibraryPtr,
    initialization: ^gdextension.Initialization,
) -> bool {
    // Initialize GDExtension ONLY
    gdextension.init(library, get_proc_address)

    initialization.initialize = initialize_game_module
    initialization.deinitialize = uninitialize_game_module
    initialization.user_data = nil
    initialization.minimum_initialization_level = .Scene

    return true
}

initialize_game_module :: proc "c" (user_data: rawptr, level: gdextension.InitializationLevel) {
    context = gdextension.godot_context()

    if level != .Scene {
        return
    }

    // Initialize Godot ONLY when we need it (in the initialize callback)
    godot.init()

    // Add debug message
    msg := godot.new_string_cstring("SUCCESS: Game GDExtension loaded!")
    defer godot.free_string(msg)
    godot.gd_print(godot.variant_from(&msg))

    // Re-enable Player class registration
    player_class_register()
}

uninitialize_game_module :: proc "c" (user_data: rawptr, level: gdextension.InitializationLevel) {
    if level != .Scene {
        return
    }

    player_class_unregister()
}
