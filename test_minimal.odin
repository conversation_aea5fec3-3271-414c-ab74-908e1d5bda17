package test

import "godot:gdextension"
import "godot:godot"

@(export)
test_init :: proc "c" (
    get_proc_address: gdextension.ExtensionInterfaceGetProcAddress,
    library: gdextension.ExtensionClassLibraryPtr,
    initialization: ^gdextension.Initialization,
) -> bool {
    gdextension.init(library, get_proc_address)
    
    // This should print to console if the DLL loads
    msg := godot.new_string_cstring("MINIMAL TEST: DLL LOADED SUCCESSFULLY!")
    defer godot.free_string(msg)
    godot.gd_print(godot.variant_from(&msg))
    
    return true
}
